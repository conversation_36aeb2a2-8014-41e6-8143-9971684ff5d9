# 用户综合流程列表API

## 概述

新增的综合流程列表API能够一次性返回当前用户的所有相关流程，包括待办任务、已办任务、发起的流程和抄送的流程。

## API接口

### 请求信息

- **URL**: `/wflow/process/instance/combinedList`
- **方法**: `POST`
- **Content-Type**: `application/json`

### 请求参数

使用 `TodoRequestDto` 作为请求体：

```json
{
  "pageSize": 20,           // 每页数量，默认20
  "pageNo": 1,              // 页码，默认1
  "code": "process_code",   // 流程定义key（可选）
  "startTimes": ["2025-01-01", "2025-01-31"],  // 时间范围（可选）
  "keyword": "关键字",       // 搜索关键字（可选）
  "form": {                 // 表单查询条件（可选）
    "field1": "value1",
    "field2": "value2"
  },
  "groupId": 1,             // 流程分组ID（可选）
  "finished": true,         // 是否已结束（可选）
  "fieldId": "field_id",    // 字段ID（可选）
  "fieldVal": "field_value" // 字段值（可选）
}
```

### 响应数据

返回分页的综合流程列表：

```json
{
  "status": 200,
  "body": {
    "current": 1,
    "size": 20,
    "total": 100,
    "records": [
      {
        "processType": "TODO",           // 流程类型：TODO/DONE/SUBMITTED/CC
        "taskId": "task_123",           // 任务ID（待办和已办时有值）
        "taskName": "审批任务",          // 任务名称
        "taskResult": "APPROVED",       // 任务处理结果（已办时有值）
        "instanceId": "instance_456",   // 流程实例ID
        "processDefId": "process_789",  // 流程定义ID
        "processDefName": "请假流程",    // 流程定义名称
        "instanceName": "张三的请假申请", // 流程实例名称
        "nodeId": "node_001",           // 流程节点ID
        "status": "RUNNING",            // 流程状态
        "result": "RUNNING",            // 流程结果
        "staterUserId": "user_001",     // 发起人用户ID
        "staterUser": {                 // 发起人信息
          "id": "user_001",
          "name": "张三",
          "avatar": "avatar_url"
        },
        "formAbstracts": [              // 表单摘要信息
          {
            "title": "请假类型",
            "value": "年假"
          }
        ],
        "startTime": "2025-01-09T10:00:00",     // 流程开始时间
        "taskCreateTime": "2025-01-09T10:30:00", // 任务创建时间
        "groupId": 1,                   // 流程分组ID
        "groupName": "人事流程"          // 流程分组名称
      }
    ]
  }
}
```

## 流程类型说明

- **TODO**: 待办任务 - 用户需要处理的任务
- **DONE**: 已办任务 - 用户已经处理过的任务  
- **SUBMITTED**: 发起的流程 - 用户作为发起人的流程实例
- **CC**: 抄送流程 - 抄送给用户的流程

## 功能特性

1. **统一查询**: 一次API调用获取所有类型的流程数据
2. **分组过滤**: 支持按流程分组进行过滤
3. **多条件搜索**: 支持按时间、关键字、表单字段等条件搜索
4. **分页支持**: 支持分页查询，提高性能
5. **类型标识**: 每条记录都有明确的类型标识
6. **时间排序**: 按时间倒序排列，最新的流程在前

## 使用示例

### 获取所有相关流程

```bash
curl -X POST "http://localhost:8080/wflow/process/instance/combinedList" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "pageNo": 1
  }'
```

### 按分组过滤

```bash
curl -X POST "http://localhost:8080/wflow/process/instance/combinedList" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "pageNo": 1,
    "groupId": 1
  }'
```

### 按关键字搜索

```bash
curl -X POST "http://localhost:8080/wflow/process/instance/combinedList" \
  -H "Content-Type: application/json" \
  -d '{
    "pageSize": 10,
    "pageNo": 1,
    "keyword": "请假"
  }'
```

## 性能优化

- 使用批量查询减少数据库访问次数
- 在内存中进行数据合并和排序
- 支持分组过滤减少不必要的数据传输
- 合理的分页机制避免大数据量问题

## 注意事项

1. 该API会查询多个数据源，响应时间可能比单一查询稍长
2. 分组过滤基于流程定义的分组配置
3. 时间排序优先使用任务创建时间，其次使用流程开始时间
4. 总数统计是各类型流程数量的简单相加，可能存在重复计算
