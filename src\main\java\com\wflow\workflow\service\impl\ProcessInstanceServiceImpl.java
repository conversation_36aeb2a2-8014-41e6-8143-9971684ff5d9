package com.wflow.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wflow.bean.do_.DeptDo;
import com.wflow.bean.do_.UserDeptDo;
import com.wflow.bean.do_.UserDo;
import com.wflow.bean.entity.*;
import com.wflow.bean.vo.UserVo;
import com.wflow.exception.BusinessException;
import com.wflow.mapper.*;
import com.wflow.service.OrgRepositoryService;
import com.wflow.service.impl.DefaultOrgRepositoryServiceImpl;
import com.wflow.service.impl.FormGroupServiceImpl;
import com.wflow.utils.FormVersionUtil;
import com.wflow.utils.JsonStreamMerger;
import com.wflow.utils.UserUtil;
import com.wflow.workflow.UELTools;
import com.wflow.workflow.bean.dto.ProcessInstanceOwnerDto;
import com.wflow.workflow.bean.process.OperationPerm;
import com.wflow.workflow.bean.process.OrgUser;
import com.wflow.workflow.bean.process.ProcessNode;
import com.wflow.workflow.bean.process.enums.ApprovalModeEnum;
import com.wflow.workflow.bean.process.enums.NodeTypeEnum;
import com.wflow.workflow.bean.process.enums.ProcessResultEnum;
import com.wflow.workflow.bean.process.form.Form;
import com.wflow.workflow.bean.process.props.ApprovalProps;
import com.wflow.workflow.bean.process.props.RootProps;
import com.wflow.workflow.bean.vo.*;
import com.wflow.workflow.config.WflowGlobalVarDef;
import com.wflow.workflow.extension.cmd.StartProcessInstanceCmdN;
import com.wflow.workflow.service.*;
import com.wflow.workflow.utils.Executor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> willian fu
 * @date : 2022/8/23
 */
@Slf4j
@Service
public class ProcessInstanceServiceImpl implements ProcessInstanceService {

    @Autowired
    private OrgRepositoryService orgRepositoryService;

    @Autowired
    private WflowModelsMapper modelsMapper;

    @Autowired
    private WflowModelHistorysMapper modelHistorysMapper;

    @Autowired
    private ProcessNodeCatchService nodeCatchService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private ManagementService managementService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private UserDeptOrLeaderService userDeptOrLeaderService;

    @Autowired
    private ProcessTaskService processTaskService;

    @Autowired
    private WflowCcTasksMapper ccTasksMapper;

    @Autowired
    private WflowSubProcessMapper subProcessMapper;

    @Autowired
    private BusinessDataStorageService businessDataService;

    @Autowired
    private UELTools uelTools;

    @Resource
    private FormGroupServiceImpl formGroupService;

    @Resource
    private WflowNodeFormInfoMapper wflowNodeFormInfoMapper;

    @Resource
    private WflowFormInfoMapper wflowFormInfoMapper;

    @Resource
    private WflowModelHistorysMapper wflowModelHistorysMapper;

    @Resource
    private DefaultOrgRepositoryServiceImpl defaultOrgRepositoryService;

    @Resource
    private WflowConfigurationfMapper configurationMapper;

    @Autowired
    private WflowModelGroupsMapper groupsMapper;

    private static final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    @Transactional
    public String startProcess(String defId, ProcessStartParamsVo params) {
        Map<String, Object> processVar = new HashMap<>();
        processVar.putAll(params.getFormData());
        processVar.putAll(params.getProcessUsers());
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(defId).latestVersion().singleResult();
        if (ObjectUtil.isNotNull(processDefinition) && processDefinition.isSuspended()) {
            throw new BusinessException("流程未启用，请先启用");
        }
        //设置发起的人员及部门信息
        String userId = UserUtil.getLoginUserId();
        //设置发起人部门ID，此处减小流程变量表数据改成只放ID
        processVar.put(WflowGlobalVarDef.START_DEPT, params.getDeptId());
        WflowModels wflowModels = modelsMapper.selectOne(new LambdaQueryWrapper<WflowModels>().eq(WflowModels::getProcessDefId, defId));
        Map<String, ProcessNode<?>> nodeMap = nodeCatchService.reloadProcessByStr(wflowModels.getProcess());
        Map<String, Object> propsMap = nodeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                v -> null == v.getValue().getProps() ? new HashMap<>() : v.getValue().getProps()));
        //将表单及流程配置设置为变量，跟随版本
        processVar.put(WflowGlobalVarDef.WFLOW_NODE_PROPS, propsMap);
        //改动把所有节点的表单存入
        String from = getFrom(defId);
        processVar.put(WflowGlobalVarDef.WFLOW_FORMS, JSONArray.parseArray(from, Form.class));
//        processVar.put(WflowGlobalVarDef.WFLOW_FORMS, JSONArray.parseArray(wflowModels.getFormItems(), Form.class));
        processVar.put(WflowGlobalVarDef.INITIATOR, userId);
        //构造流程实例名称
        String instanceName = orgRepositoryService.getUserById(userId).getUserName() + "发起的" + processDefinition.getName();
        //这样做貌似无效果，变量表不会多INITIATOR变量，但是流程表发起人有效
        Authentication.setAuthenticatedUserId(userId);
        ProcessInstance processInstance = managementService.executeCommand(new StartProcessInstanceCmdN<>(
                instanceName, defId, null, processVar, null));
        businessDataService.saveInstanceFormData(processInstance.getProcessInstanceId(), params.getFormData());
        log.info("启动 {} 流程实例 {} 成功", processInstance.getProcessDefinitionName(), processInstance.getProcessInstanceId());
        //自动完成发起人节点任务，发起人是一个UserTask，发起后触发start事件然后分配Task给发起人，所以这里要自动完成
        Task rootTask = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).active().singleResult();
        if (Objects.nonNull(rootTask)) {
            rootTask.setDescription(ProcessHandlerParamsVo.Action.complete.toString());
            taskService.complete(rootTask.getId());
        }
        Authentication.setAuthenticatedUserId(null);
        return processInstance.getProcessInstanceId();
    }

    @Override
    public String getBeforeTask(String instanceId, String task) {
        return null;
    }

    @Override
    @Transactional
    public void delProcessInstance(String instanceId) {
        // 删除流程实例
        if(isAtInitiatorNode(instanceId)){
            throw new BusinessException("非发起人节点不能删除");
        }
        try {
            runtimeService.deleteProcessInstance(instanceId, "删除");
        } catch (Exception ignored) {
        }
        // 删除流程历史实例
        historyService.deleteHistoricProcessInstance(instanceId);
        //删除的同时相关抄送表数据也要删除，不然分页数据会错
        ccTasksMapper.delete(new LambdaQueryWrapper<WflowCcTasks>().eq(WflowCcTasks::getInstanceId, instanceId));
        //删除物理表表单数据及修改记录
        businessDataService.deleteFormData(instanceId);
        log.info("删除流程实例[{}]成功", instanceId);
    }

    @Override
    public ProcessProgressVo getInstanceProgress(String nodeId, String instanceId) {
        //先查实例，然后判断是子流程还是主流程
        HistoricProcessInstance instance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(instanceId).singleResult();
        //数据分类 表单配置及数据、审批任务结果、
        ProcessInstanceOwnerDto owner = null;
        List<Form> forms = Collections.emptyList();
        Map<String, Object> formDatas = new HashMap<>();
        Map<String, ProcessHandlerParamsVo.Action> approvalResults = new HashMap<>();
        Map<String, Object> nodeProps = new HashMap<>();
        Map<String, String> signs = new HashMap<>();
        //是否是子流程
        boolean isSub = StrUtil.isNotBlank(instance.getSuperProcessInstanceId());
        HistoricProcessInstance mainInst = isSub ? null : instance;
        if (isSub) {
            //查出主流程表单数据
            mainInst = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(instance.getSuperProcessInstanceId()).singleResult();
            formDatas = businessDataService.getProcessInstanceFormData(mainInst.getId());
//            formDatas = businessDataService.getProcessNodeFormData(mainInst.getId(),nodeId);
        }
        //优化查询，把之前查好几次的一次性查出来然后再分类
        List<HistoricVariableInstance> variables = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(instanceId).executionId(instanceId).list();
        Map<String, Object> vars = Objects.nonNull(instance.getEndTime()) ?
                new HashMap<>() : uelTools.getContextVar(instanceId, instance.getProcessDefinitionId());
        //遍历所有变量，将数据分类
        for (HistoricVariableInstance var : variables) {
            vars.put(var.getVariableName(), var.getValue());
            if (var.getVariableName().startsWith(WflowGlobalVarDef.TASK_RES_PRE)) {
                approvalResults.put(var.getVariableName(), (ProcessHandlerParamsVo.Action) var.getValue());
            } else if (!isSub && var.getVariableName().startsWith("field")) {
                formDatas.put(var.getVariableName(), var.getValue());
            } else if (var.getVariableName().startsWith("sign_")) {
                signs.put(var.getVariableName().substring(5), String.valueOf(var.getValue()));
            } else if (!isSub && WflowGlobalVarDef.WFLOW_FORMS.equals(var.getVariableName())) {
                forms = (List<Form>) var.getValue();
            } else if (WflowGlobalVarDef.WFLOW_NODE_PROPS.equals(var.getVariableName())) {
                nodeProps = (Map<String, Object>) var.getValue();
            } else if (WflowGlobalVarDef.OWNER.equals(var.getVariableName())) {
                owner = (ProcessInstanceOwnerDto) var.getValue();
            } else if (WflowGlobalVarDef.START_DEPT.equals(var.getVariableName())) {
                String key = instance.getStartUserId() + "_" + var.getValue();
                Map<String, UserDeptDo> infoMap = orgRepositoryService.getUserDeptInfos(CollectionUtil.newArrayList(key));
                UserDeptDo userDeptDo = infoMap.getOrDefault(key, new UserDeptDo());
                owner = ProcessInstanceOwnerDto.builder().ownerDeptId(String.valueOf(var.getValue()))
                        .owner(instance.getStartUserId()).ownerName(userDeptDo.getUserName())
                        .ownerDeptId(userDeptDo.getDeptId()).ownerDeptName(userDeptDo.getDeptName()).build();
            }
        }
        ProcessNode<?> currentNode = null;
        String modelFormConfig = null;
        ProcessProgressVo.InstanceExternSetting externSetting = null;
        OperationPerm operationPerms = null;
        Map<String, ProcessNode<?>> nodeMap = Collections.emptyMap();
        if (isSub) {
            WflowSubProcess subProcess = subProcessMapper.selectOne(new LambdaQueryWrapper<>(WflowSubProcess.builder()
                    .procDefId(instance.getProcessDefinitionId()).build()));
            nodeMap = nodeCatchService.reloadProcessByStr(subProcess.getProcess());
            currentNode = nodeMap.get(nodeId);
            operationPerms = getOperationPerm(currentNode);
            HistoricVariableInstance formsVar = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(mainInst.getId()).variableName(WflowGlobalVarDef.WFLOW_FORMS).singleResult();
            forms = (List<Form>) formsVar.getValue();
            //获取主流程节点配置信息
            nodeProps = getNodeProps(nodeMap);
        }
        //搜索当前版本流程的配置
        WflowModelHistorys modelHistory = modelHistorysMapper.selectOne(new LambdaQueryWrapper<>(WflowModelHistorys.builder()
                .processDefId(mainInst.getProcessDefinitionId()).version(mainInst.getProcessDefinitionVersion()).build()));
        if (StrUtil.isNotBlank(nodeId)) {
            nodeMap = nodeCatchService.reloadProcessByStr(modelHistory.getProcess());
            currentNode = nodeMap.get(isSub ? instance.getBusinessKey() : nodeId);
            if (!isSub) {
                operationPerms = getOperationPerm(currentNode);
            }
            modelFormConfig = modelHistory.getFormConfig();
            externSetting = getExternSetting(modelHistory.getSettings(), instance.getStartUserId());
            nodeProps = getNodeProps(nodeMap);
        }
        if (Objects.isNull(forms)){
            forms = JSONArray.parseArray(modelHistory.getFormItems(), Form.class);
//            forms = JSONArray.parseArray(getNodeFrom(modelHistory.getProcessDefId(),nodeId), Form.class);
        }
        if (Objects.isNull(nodeProps)){
            nodeProps = getNodeProps(nodeCatchService.reloadProcessByStr(modelHistory.getProcess()));
        }
        List<Form> formItems;
        //表单的 currentNode 以主流程为准d
        boolean flag = FormVersionUtil.getFormVersion(modelHistory.getFormItems());
        List<WflowFormDir> formDirs = Collections.emptyList();
        if(!flag) {
            formItems = businessDataService.filterFormByPermConfigForNode(forms, formDatas, currentNode);
            String dirData = Optional.ofNullable(configurationMapper.selectOne(Wrappers.<WflowConfiguration>lambdaQuery()
                            .select(WflowConfiguration::getDirData)
                            .eq(WflowConfiguration::getModelId, instanceId))).map(WflowConfiguration::getDirData)
                    .orElse("");
            if(StrUtil.isNotBlank(dirData)) {
                formDirs = JSONArray.parseArray(dirData, WflowFormDir.class);
            } else {
                formDirs = formGroupService.getTree(null,instance.getProcessDefinitionId());
            }

        }else {
            formItems = businessDataService.filterFormAndDataByPermConfig(forms, formDatas, currentNode);
        }
        //下面都是一样的
        UserDo users = orgRepositoryService.getUserById(instance.getStartUserId());
        OrgUser startUser = OrgUser.builder().id(users.getUserId()).name(users.getUserName()).avatar(users.getAvatar()).build();
        List<ProcessProgressVo.ProgressNode> taskRecords = getHisTaskRecords(instanceId, nodeProps, approvalResults, signs, instance.getEndTime());
        //添加抄送
        taskRecords.addAll(getCcTaskRecords(instanceId));
        if (ObjectUtil.isNull(instance.getEndTime())) {
            if (owner != null) {
                taskRecords.addAll(processTaskService.getFutureTask(instance, owner.getOwnerDeptId(), vars, nodeMap));
            }
        }
        //按开始时间对节点进行排序
        taskRecords = taskRecords.stream()
                .sorted(Comparator.comparing(ProcessProgressVo.ProgressNode::getStartTime))
                .collect(Collectors.toList());

        // 提取用户ID并查询用户所属部门信息
        enrichTaskRecordsWithDeptInfo(taskRecords);

        ProcessProgressVo.ProgressNode progressNode = taskRecords.stream()
                .filter(n -> "root".equals(n.getNodeId()) && Objects.isNull(n.getResult()))
                .findFirst().orElse(null);
        if (Objects.nonNull(progressNode)) {
            //辅助前端UI构建，第一条一般都是
            progressNode.setResult(ProcessHandlerParamsVo.Action.complete);
            progressNode.setNodeType(NodeTypeEnum.ROOT);
        } else {
            //没有就进行构建，兼容之前的流程
            taskRecords.add(0, ProcessProgressVo.ProgressNode.builder()
                    .nodeId("root")
                    .name(isSub ? "发起子流程" : "提交申请")
                    .user(startUser)
                    .nodeType(NodeTypeEnum.ROOT)
                    .startTime(instance.getStartTime())
                    .finishTime(instance.getStartTime())
                    .taskId("root")
                    .result(ProcessHandlerParamsVo.Action.complete)
                    .build());
        }
        //提取全量表单数据
        if (StrUtil.isBlank(modelFormConfig)) {
            modelFormConfig = modelHistory.getFormConfig();
            externSetting = getExternSetting(modelHistory.getSettings(), instance.getStartUserId());
        }
        ProcessResultEnum processResult = ProcessResultEnum.resolveResult(instance.getEndActivityId());
        return ProcessProgressVo.builder()
                .instanceId(instanceId)
                .version(instance.getProcessDefinitionVersion())
                .formItems(formItems)
                .formConfig(JSONObject.parseObject(modelFormConfig))
                .formData(formDatas)
                .processDefName(instance.getProcessDefinitionName())
                .staterUser(startUser)
                .starterDept(null == owner ? null : owner.getOwnerDeptName())
                .status(processResult.getDesc())
                .result(processResult)
                .startTime(instance.getStartTime())
                .finishTime(instance.getEndTime())
                .progress(taskRecords)
                .operationPerm(operationPerms)
                .externSetting(externSetting)
                .formDirs(formDirs)
                .formId(modelHistory.getFormId())
                .materials(modelHistory.getMaterials())
                .materialsRules(JSONArray.parseArray(modelHistory.getMaterialsRules()))
                .build();
    }

    /**
     * 获取流程的审批历史记录
     *
     * @param instanceId 审批实例ID
     * @param nodeProps  节点设置
     * @param varMap     变量
     * @param signs      签名
     * @param instEndTime 流程实例结束时间
     * @return 历史记录列表
     */

    private List<ProcessProgressVo.ProgressNode> getHisTaskRecords(String instanceId, Map<String, Object> nodeProps,
                                                                   Map<String, ProcessHandlerParamsVo.Action> varMap,
                                                                   Map<String, String> signs, Date instEndTime) {
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(instanceId).orderByHistoricActivityInstanceStartTime().asc().list();
        Set<String> userSet = new HashSet<>();
        //获取节点处理结果
        Map<String, List<TaskCommentVo>> commentMap = new HashMap<>();
        //统一处理所有评论数据，省的多次查询
        List<TaskCommentVo> cmvos = taskService.getProcessInstanceComments(instanceId).stream().map(comment -> {
            userSet.add(comment.getUserId());
            TaskCommentVo commentVo = TaskCommentVo.builder()
                    .id(comment.getId())
                    .taskId(comment.getTaskId())
                    .commentType(comment.getType())
                    .type("COMMENT")
                    .createTime(comment.getTime())
                    .user(OrgUser.builder().id(comment.getUserId()).build())
                    .build();
            ProcessHandlerParamsVo.ProcessComment processComment = JSONObject.parseObject(comment.getFullMessage(), ProcessHandlerParamsVo.ProcessComment.class);
            commentVo.setText(processComment.getText());
            commentVo.setAttachments(processComment.getAttachments());
            return commentVo;
        }).collect(Collectors.toList());
        cmvos.forEach(cm -> {
            //把评论数据按照task进行归类
            String taskId = Optional.ofNullable(cm.getTaskId()).orElse(instanceId);
            List<TaskCommentVo> vos = commentMap.computeIfAbsent(taskId, k -> new ArrayList<>());
            vos.add(cm);
        });
        List<ProcessProgressVo.ProgressNode> progressNodes = list.stream()
                .filter(his -> ObjectUtil.isNotNull(his.getTaskId()) || "callActivity".equals(his.getActivityType()))
                .map(his -> {
                    Object props = nodeProps.get(his.getActivityId());
                    List<TaskCommentVo> commentVos = commentMap.getOrDefault(his.getTaskId(), Collections.emptyList());
                    ProcessProgressVo.ProgressNode node = ProcessProgressVo.ProgressNode.builder()
                            .nodeId(his.getActivityId())
                            .isFuture(false)
                            .name(his.getActivityName())
                            .startTime(his.getStartTime())
                            .finishTime(Optional.ofNullable(his.getEndTime()).orElse(instEndTime))
                            .comment(commentVos).build();
                    if ("callActivity".equals(his.getActivityType())) {
                        node.setNodeType(NodeTypeEnum.SUBPROC);
                        //取子流程实例
                        HistoricProcessInstance subInst = historyService.createHistoricProcessInstanceQuery()
                                .processInstanceId(his.getCalledProcessInstanceId())
                                .singleResult();
                        userSet.add(subInst.getStartUserId());
                        node.setUser(OrgUser.builder().id(subInst.getStartUserId()).build());
                        node.setTaskId(subInst.getId());
                        ProcessResultEnum result = ProcessResultEnum.resolveResult(subInst.getEndActivityId());
                        switch (result) {
                            case PASS:
                                node.setResult(ProcessHandlerParamsVo.Action.agree);
                                break;
                            case REFUSE:
                                node.setResult(ProcessHandlerParamsVo.Action.refuse);
                                break;
                            case CANCEL:
                                node.setResult(ProcessHandlerParamsVo.Action.cancel);
                                break;
                        }
                    } else {
                        ApprovalModeEnum approvalMode = null;
                        if (props instanceof ApprovalProps) {
                            approvalMode = ((ApprovalProps) props).getMode();
                        }
                        userSet.add(his.getAssignee());
                        node.setNodeType(NodeTypeEnum.APPROVAL);
                        node.setApprovalMode(approvalMode);
                        node.setUser(OrgUser.builder().id(his.getAssignee()).build());
                        node.setTaskId(his.getTaskId());
                        node.setSignature(signs.get(his.getTaskId()));
                        node.setResult(varMap.get(WflowGlobalVarDef.TASK_RES_PRE + his.getTaskId()));
                    }
                    return node;
                }).collect(Collectors.toList());
        //将非任务类的评论转换成流程节点
        progressNodes.addAll(commentMap.getOrDefault(instanceId, Collections.emptyList()).stream().map(cm ->
                ProcessProgressVo.ProgressNode.builder()
                        .nodeId(cm.getId())
                        .name("参与评论")
                        .user(cm.getUser())
                        .startTime(cm.getCreateTime())
                        .finishTime(cm.getCreateTime())
                        .taskId(cm.getId())
                        .comment(CollectionUtil.newArrayList(cm))
                        .result(ProcessHandlerParamsVo.Action.comment)
                        .build()).collect(Collectors.toList()));
        if (CollectionUtil.isNotEmpty(userSet)) {
            //过滤掉系统节点
            Map<String, OrgUser> map = userDeptOrLeaderService.getUserMapByIds(userSet.stream()
                    .filter(v -> !WflowGlobalVarDef.WFLOW_TASK_AGRRE.equals(v) && !WflowGlobalVarDef.WFLOW_TASK_REFUSE.equals(v))
                    .collect(Collectors.toSet()));
            progressNodes.forEach(n -> {
                if (WflowGlobalVarDef.WFLOW_TASK_AGRRE.equals(n.getUser().getId())
                        || WflowGlobalVarDef.WFLOW_TASK_REFUSE.equals(n.getUser().getId())) {
                    n.setUser(WflowGlobalVarDef.SYS);
                } else {
                    n.setUser(map.get(n.getUser().getId()));
                    n.getComment().forEach(c -> c.setUser(map.get(c.getUser().getId())));
                }
            });
        }
        return progressNodes;
    }

    @Override
    public List<Task> getProcessInstanceTaskList(String instanceId) {
        return taskService.createTaskQuery().processInstanceId(instanceId).active().list();
    }

    @Override
    public Page<ProcessInstanceVo> getUserSubmittedList(Integer pageSize, Integer pageNo, String startUser, String code,
            Boolean finished, String[] startTimes, String keyword, String fieldId, String fieldVal,
            Map<String, Object> form, String type) {

        HistoricProcessInstanceQuery instanceQuery = historyService.createHistoricProcessInstanceQuery();
        Executor.builder()
                .ifNotBlankNext(startUser, instanceQuery::startedBy)
                .ifNotBlankNext(code, instanceQuery::processDefinitionKey)
                .ifTrueNext(null != startTimes && startTimes.length > 1, () -> {
                    instanceQuery.startedAfter(DateUtil.parse(startTimes[0]));
                    instanceQuery.startedBefore(DateUtil.parse(startTimes[1]));
                })
                .ifNotBlankNext(keyword, v -> instanceQuery.processInstanceNameLike(StrUtil.format("%{}%", keyword)))
                .ifTrueNext(Boolean.TRUE.equals(finished), instanceQuery::finished)
                .ifTrueNext(Boolean.FALSE.equals(finished), instanceQuery::unfinished)
                .ifNotBlankNext(fieldId, id -> {
                    if (StrUtil.isBlank(code)){
                        throw new BusinessException("搜索表单值必须先指定表单流程类型");
                    }
                    if (StrUtil.isNotBlank(fieldVal)){
                        instanceQuery.variableValueLike(fieldId, "%" + fieldVal + "%");
                    }
                })
                .ifNotNullNext(form,val->{
                    for (Map.Entry<String, Object> entry : val.entrySet()){
                        String key = entry.getKey();
                        Object value = entry.getValue();
                        // 使用链式AND条件
                        instanceQuery.variableValueLike(key, "%"+value+"%");

                    }
                })

        ;

        List<HistoricProcessInstance> historicProcessInstances = instanceQuery
                .orderByProcessInstanceStartTime().desc()
                .orderByProcessInstanceEndTime().desc()
                .listPage(pageSize * (pageNo - 1), pageSize);
        Page<ProcessInstanceVo> page = new Page<>();
        page.setTotal(instanceQuery.count());
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        Map<String, String> instanceNodeMap = new HashMap<>();
        page.setRecords(getInstances(instanceNodeMap, historicProcessInstances));
        return page;
    }

    @Override
    public Page<ProcessInstanceVo> getCcMeInstance(Integer pageSize, Integer pageNo, String code, String[] startTimes,Map<String, Object> form) {
        LambdaQueryWrapper<WflowCcTasks> queryWrapper = new LambdaQueryWrapper<WflowCcTasks>()
                .eq(WflowCcTasks::getUserId, UserUtil.getLoginUserId());
        HistoricProcessInstanceQuery instanceQuery = historyService.createHistoricProcessInstanceQuery();
        Executor.builder()
                .ifNotBlankNext(code, v -> queryWrapper.eq(WflowCcTasks::getCode, code))
                .ifNotBlankNext(code, instanceQuery::processDefinitionKey)
                .ifTrueNext(null != startTimes && startTimes.length > 1, () -> {
                    queryWrapper.ge(WflowCcTasks::getCreateTime, DateUtil.parse(startTimes[0]));
                    queryWrapper.le(WflowCcTasks::getCreateTime, DateUtil.parse(startTimes[1]));
                })
        ;
        Page<WflowCcTasks> tasks = ccTasksMapper.selectPage(new Page<>(pageNo, pageSize),
                queryWrapper.orderByDesc(WflowCcTasks::getCreateTime));
        Map<String, String> instanceMap = tasks.getRecords().stream().collect(
                Collectors.toMap(WflowCcTasks::getInstanceId, WflowCcTasks::getNodeId, (v1, v2) -> v2));
        Page<ProcessInstanceVo> page = new Page<>();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        if (CollectionUtil.isNotEmpty(instanceMap)) {
            HistoricProcessInstanceQuery query  = instanceQuery
                    .processInstanceIds(instanceMap.keySet());
            // 添加模糊查询条件
            if(CollectionUtil.isNotEmpty(form)){
                for (Map.Entry<String, Object> entry : form.entrySet()){
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    // 使用链式AND条件
                    query = query.variableValueLike(key, "%"+value+"%");

                }
            }
            List<HistoricProcessInstance> historicProcessInstances = query
                    .orderByProcessInstanceStartTime().desc()
                    .orderByProcessInstanceEndTime().desc().list();
            List<ProcessInstanceVo> instances = getInstances(instanceMap, historicProcessInstances);
            //构造key，用于后面判断是否有缺失的instanceId和nodeId
            Set<String> ccInstKeys = new HashSet<>();
            Map<String, ProcessInstanceVo> voMap = instances.stream().peek(v -> ccInstKeys.add(v.getInstanceId() + v.getNodeId()))
                    .collect(Collectors.toMap(ProcessInstanceVo::getInstanceId , v -> v));
            //tasks里面与instanceMap数量不一致时，需要找到缺少的instanceId和nodeId，补记录
            if (tasks.getSize() != instances.size()) {
                tasks.getRecords().stream().filter(v -> !ccInstKeys.contains(v.getInstanceId() + v.getNodeId()))
                        .forEach(v -> {
                            ProcessInstanceVo vo = ProcessInstanceVo.builder().build();
                            BeanUtils.copyProperties(voMap.get(v.getInstanceId()), vo);
                            vo.setNodeId(v.getNodeId());
                            instances.add(vo);
                        });
                //对instances进行startTime倒序排序
                instances.sort(Comparator.comparing(ProcessInstanceVo::getStartTime).reversed());
            }
            page.setTotal(tasks.getTotal());
            page.setRecords(instances);
        } else {
            page.setTotal(0);
            page.setRecords(Collections.emptyList());
        }
        return page;
    }

    @Override
    public InstanceCountVo getProcessInstanceCount() {
        String userId = UserUtil.getLoginUserId();
        Long cc = ccTasksMapper.selectCount(new LambdaQueryWrapper<WflowCcTasks>().eq(WflowCcTasks::getUserId, userId));
        TaskQuery taskQuery = taskService.createTaskQuery();
        taskQuery.active().taskCandidateOrAssigned(userId);
        List<Task> tasks = taskQuery.list();
        Map<String, ProcessInstance> instanceMap = CollectionUtil.isNotEmpty(tasks) ?
                runtimeService.createProcessInstanceQuery().processInstanceIds(tasks.stream()
                                .map(Task::getProcessInstanceId).collect(Collectors.toSet()))
                        .list().stream().collect(Collectors.toMap(ProcessInstance::getId, v -> v)) : new HashMap<>();
        List<String> taskList = tasks.stream().map(task -> {
            String todo = task.getAssignee();
            ProcessInstance instance = instanceMap.get(task.getProcessInstanceId());
            List<Long> inList = new ArrayList<>();
            inList.add(Long.valueOf(instance.getStartUserId()));
            inList.add(Long.valueOf(task.getAssignee()));
            boolean flag = defaultOrgRepositoryService.getInstitution(inList, task.getProcessDefinitionId(), task.getTaskDefinitionKey());
            if (flag) {
                return todo;
            } else {
                return null;
            }
        }).filter(Objects::nonNull).toList();
        Long mySubmited = historyService.createHistoricProcessInstanceQuery().startedBy(userId).count();
        // 1. 查询该用户完成的所有历史任务（获取流程实例ID集合）
        Set<String> processInstanceIds = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(userId)
                .finished()
                .taskDefinitionKeyLike("node_%")
                .list()
                .stream()
                .map(HistoricTaskInstance::getProcessInstanceId)
                .collect(Collectors.toSet());
        long total = 0L;
        long done = 0L;
        if (!processInstanceIds.isEmpty()) {
            total = historyService
                    .createHistoricProcessInstanceQuery()
                    .processInstanceIds(processInstanceIds)
                    .finished().count();
            done = historyService
                    .createHistoricProcessInstanceQuery()
                    .processInstanceIds(processInstanceIds)
                    .unfinished().count();
        }
        return InstanceCountVo.builder().todo(taskList.stream().count()).mySubmited(mySubmited).cc(cc.intValue()).end(total).processed(done).build();
    }

    @Override
    public ResponseEntity<Object> deleteReason(String instanceId) {
        String userId = UserUtil.getLoginUserId();
        if (runtimeService.createProcessInstanceQuery()
                .processInstanceId(instanceId)
                .singleResult() == null) {
            return ResponseEntity.notFound().build();
        }

        runtimeService.deleteProcessInstance(instanceId,userId+"手动终止流程");
        return  ResponseEntity.ok("流程已终止");
    }

    @Override
    public Object getHistoryList(Integer pageSize, Integer pageNo, Object o, String code, Boolean finished,
            String[] startTimes, String keyword, String fieldId, String fieldVal, Object o1, String type) {

        return null;
    }

    @Override
    public Page<CombinedProcessVo> getUserCombinedProcessList(Integer pageSize, Integer pageNo, String code,
            String[] startTimes, String keyword,
            Map<String, Object> form, Long groupId) {
        String userId = UserUtil.getLoginUserId();
        List<CombinedProcessVo> allProcesses = new ArrayList<>();

        // 预先加载分组名称映射，提高性能
        Map<Long, String> groupNameMap = getGroupNameMap();

        // 为了提高性能，我们使用较大的页面大小来获取更多数据，然后在内存中进行合并和排序
        // 获取更多数据以确保有足够的记录进行排序和分页
        int fetchSize = Math.max(pageSize * 3, 100);

        // 1. 获取待办任务
        Page<ProcessTaskVo> todoPage = processTaskService.getUserTodoList(fetchSize, 1, code, startTimes, keyword,
                form, null);
        List<CombinedProcessVo> todoProcesses = todoPage.getRecords().stream()
                .filter(task -> groupId == null || isProcessInGroup(task.getProcessDefId(), groupId))
                .map(task -> convertTodoToCombined(task, groupNameMap))
                .collect(Collectors.toList());
        allProcesses.addAll(todoProcesses);

        // 2. 获取已办任务
        Page<ProcessTaskVo> doneePage = processTaskService.getUserIdoList(fetchSize, 1, code, null, form);
        List<CombinedProcessVo> doneProcesses = doneePage.getRecords().stream()
                .filter(task -> groupId == null || isProcessInGroup(task.getProcessDefId(), groupId))
                .map(task -> convertDoneToCombined(task, groupNameMap))
                .collect(Collectors.toList());
        allProcesses.addAll(doneProcesses);

        // 3. 获取发起的流程
        Page<ProcessInstanceVo> submittedPage = getUserSubmittedList(fetchSize, 1, userId, code, null, startTimes,
                keyword, null, null, form, null);
        List<CombinedProcessVo> submittedProcesses = submittedPage.getRecords().stream()
                .filter(instance -> groupId == null || isProcessInGroup(instance.getProcessDefId(), groupId))
                .map(instance -> convertSubmittedToCombined(instance, groupNameMap))
                .collect(Collectors.toList());
        allProcesses.addAll(submittedProcesses);

        // 4. 获取抄送的流程
        Page<ProcessInstanceVo> ccPage = getCcMeInstance(fetchSize, 1, code, startTimes, form);
        List<CombinedProcessVo> ccProcesses = ccPage.getRecords().stream()
                .filter(instance -> groupId == null || isProcessInGroup(instance.getProcessDefId(), groupId))
                .map(instance -> convertCcToCombined(instance, groupNameMap))
                .collect(Collectors.toList());
        allProcesses.addAll(ccProcesses);

        // 5. 按时间排序
        allProcesses.sort((a, b) -> {
            Date timeA = a.getTaskCreateTime() != null ? a.getTaskCreateTime() : a.getStartTime();
            Date timeB = b.getTaskCreateTime() != null ? b.getTaskCreateTime() : b.getStartTime();
            if (timeA == null && timeB == null) {
                return 0;
            }
            if (timeA == null) {
                return 1;
            }
            if (timeB == null) {
                return -1;
            }
            return timeB.compareTo(timeA); // 降序排列
        });

        // 6. 手动分页
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, allProcesses.size());
        List<CombinedProcessVo> pagedProcesses = start < allProcesses.size() ? allProcesses.subList(start, end)
                : Collections.emptyList();

        // 7. 计算总数（这里简化处理，实际应该是所有类型的总数）
        long totalCount = todoPage.getTotal() + doneePage.getTotal() + submittedPage.getTotal() + ccPage.getTotal();

        Page<CombinedProcessVo> result = new Page<>();
        result.setCurrent(pageNo);
        result.setSize(pageSize);
        result.setTotal(totalCount);
        result.setRecords(pagedProcesses);

        return result;
    }

    /**
     * 获取分组名称映射
     */
    private Map<Long, String> getGroupNameMap() {
        try {
            return groupsMapper.selectList(new LambdaQueryWrapper<WflowModelGroups>())
                    .stream()
                    .collect(Collectors.toMap(WflowModelGroups::getGroupId, WflowModelGroups::getGroupName));
        } catch (Exception e) {
            log.warn("获取分组名称映射时发生异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 检查流程是否属于指定分组
     */
    private boolean isProcessInGroup(String processDefId, Long groupId) {
        if (groupId == null) {
            return true; // 如果没有指定分组，则不过滤
        }

        try {
            // 通过流程定义ID查询对应的模型信息
            WflowModels model = modelsMapper.selectByProcessDefId(processDefId);
            if (model != null) {
                return groupId.equals(model.getGroupId());
            }
        } catch (Exception e) {
            log.warn("检查流程分组时发生异常，processDefId: {}, groupId: {}", processDefId, groupId, e);
        }

        return false; // 如果查询不到模型信息，则过滤掉
    }

    /**
     * 获取流程的分组信息
     */
    private ProcessGroupInfo getProcessGroupInfo(String processDefId) {
        try {
            WflowModels model = modelsMapper.selectByProcessDefId(processDefId);
            if (model != null) {
                return new ProcessGroupInfo(model.getGroupId(), null);
            }
        } catch (Exception e) {
            log.warn("获取流程分组信息时发生异常，processDefId: {}", processDefId, e);
        }
        return new ProcessGroupInfo(null, null);
    }

    /**
     * 流程分组信息内部类
     */
    private static class ProcessGroupInfo {
        private final Long groupId;
        private final String groupName;

        public ProcessGroupInfo(Long groupId, String groupName) {
            this.groupId = groupId;
            this.groupName = groupName;
        }

        public Long getGroupId() {
            return groupId;
        }

        public String getGroupName() {
            return groupName;
        }
    }

    /**
     * 将待办任务转换为综合流程VO
     */
    private CombinedProcessVo convertTodoToCombined(ProcessTaskVo task) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(task.getProcessDefId());
        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.TODO)
                .taskId(task.getTaskId())
                .taskDefKey(task.getTaskDefKey())
                .taskName(task.getTaskName())
                .instanceId(task.getInstanceId())
                .processDefId(task.getProcessDefId())
                .processDefName(task.getProcessDefName())
                .nodeId(task.getNodeId())
                .staterUserId(task.getOwnerId())
                .staterUser(task.getOwner())
                .formAbstracts(task.getFormAbstracts())
                .startTime(task.getCreateTime())
                .taskCreateTime(task.getTaskCreateTime())
                .deployId(task.getDeployId())
                .version(task.getVersion())
                .superInstanceId(task.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .build();
    }

    /**
     * 将待办任务转换为综合流程VO（带分组名称映射）
     */
    private CombinedProcessVo convertTodoToCombined(ProcessTaskVo task, Map<Long, String> groupNameMap) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(task.getProcessDefId());
        String groupName = groupInfo.getGroupId() != null ? groupNameMap.get(groupInfo.getGroupId()) : null;

        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.TODO)
                .taskId(task.getTaskId())
                .taskDefKey(task.getTaskDefKey())
                .taskName(task.getTaskName())
                .instanceId(task.getInstanceId())
                .processDefId(task.getProcessDefId())
                .processDefName(task.getProcessDefName())
                .nodeId(task.getNodeId())
                .staterUserId(task.getOwnerId())
                .staterUser(task.getOwner())
                .formAbstracts(task.getFormAbstracts())
                .startTime(task.getCreateTime())
                .taskCreateTime(task.getTaskCreateTime())
                .deployId(task.getDeployId())
                .version(task.getVersion())
                .superInstanceId(task.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .groupName(groupName)
                .build();
    }

    /**
     * 将已办任务转换为综合流程VO
     */
    private CombinedProcessVo convertDoneToCombined(ProcessTaskVo task) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(task.getProcessDefId());
        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.DONE)
                .taskId(task.getTaskId())
                .taskDefKey(task.getTaskDefKey())
                .taskName(task.getTaskName())
                .taskResult(task.getTaskResult())
                .instanceId(task.getInstanceId())
                .processDefId(task.getProcessDefId())
                .processDefName(task.getProcessDefName())
                .nodeId(task.getNodeId())
                .staterUserId(task.getOwnerId())
                .staterUser(task.getOwner())
                .formAbstracts(task.getFormAbstracts())
                .startTime(task.getCreateTime())
                .taskCreateTime(task.getTaskCreateTime())
                .taskEndTime(task.getTaskEndTime())
                .deployId(task.getDeployId())
                .version(task.getVersion())
                .superInstanceId(task.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .build();
    }

    /**
     * 将已办任务转换为综合流程VO（带分组名称映射）
     */
    private CombinedProcessVo convertDoneToCombined(ProcessTaskVo task, Map<Long, String> groupNameMap) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(task.getProcessDefId());
        String groupName = groupInfo.getGroupId() != null ? groupNameMap.get(groupInfo.getGroupId()) : null;

        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.DONE)
                .taskId(task.getTaskId())
                .taskDefKey(task.getTaskDefKey())
                .taskName(task.getTaskName())
                .taskResult(task.getTaskResult())
                .instanceId(task.getInstanceId())
                .processDefId(task.getProcessDefId())
                .processDefName(task.getProcessDefName())
                .nodeId(task.getNodeId())
                .staterUserId(task.getOwnerId())
                .staterUser(task.getOwner())
                .formAbstracts(task.getFormAbstracts())
                .startTime(task.getCreateTime())
                .taskCreateTime(task.getTaskCreateTime())
                .taskEndTime(task.getTaskEndTime())
                .deployId(task.getDeployId())
                .version(task.getVersion())
                .superInstanceId(task.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .groupName(groupName)
                .build();
    }

    /**
     * 将发起的流程转换为综合流程VO
     */
    private CombinedProcessVo convertSubmittedToCombined(ProcessInstanceVo instance) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(instance.getProcessDefId());
        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.SUBMITTED)
                .instanceId(instance.getInstanceId())
                .processDefId(instance.getProcessDefId())
                .processDefName(instance.getProcessDefName())
                .instanceName(instance.getInstanceName())
                .nodeId(instance.getNodeId())
                .taskName(instance.getTaskName())
                .status(instance.getStatus())
                .result(instance.getResult())
                .staterUserId(instance.getStaterUserId())
                .staterUser(instance.getStaterUser())
                .formAbstracts(instance.getFormAbstracts())
                .startTime(instance.getStartTime())
                .finishTime(instance.getFinishTime())
                .deployId(instance.getDeployId())
                .version(instance.getVersion())
                .formId(instance.getFormId())
                .superInstanceId(instance.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .build();
    }

    /**
     * 将发起的流程转换为综合流程VO（带分组名称映射）
     */
    private CombinedProcessVo convertSubmittedToCombined(ProcessInstanceVo instance, Map<Long, String> groupNameMap) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(instance.getProcessDefId());
        String groupName = groupInfo.getGroupId() != null ? groupNameMap.get(groupInfo.getGroupId()) : null;

        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.SUBMITTED)
                .instanceId(instance.getInstanceId())
                .processDefId(instance.getProcessDefId())
                .processDefName(instance.getProcessDefName())
                .instanceName(instance.getInstanceName())
                .nodeId(instance.getNodeId())
                .taskName(instance.getTaskName())
                .status(instance.getStatus())
                .result(instance.getResult())
                .staterUserId(instance.getStaterUserId())
                .staterUser(instance.getStaterUser())
                .formAbstracts(instance.getFormAbstracts())
                .startTime(instance.getStartTime())
                .finishTime(instance.getFinishTime())
                .deployId(instance.getDeployId())
                .version(instance.getVersion())
                .formId(instance.getFormId())
                .superInstanceId(instance.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .groupName(groupName)
                .build();
    }

    /**
     * 将抄送的流程转换为综合流程VO
     */
    private CombinedProcessVo convertCcToCombined(ProcessInstanceVo instance) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(instance.getProcessDefId());
        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.CC)
                .instanceId(instance.getInstanceId())
                .processDefId(instance.getProcessDefId())
                .processDefName(instance.getProcessDefName())
                .instanceName(instance.getInstanceName())
                .nodeId(instance.getNodeId())
                .taskName(instance.getTaskName())
                .status(instance.getStatus())
                .result(instance.getResult())
                .staterUserId(instance.getStaterUserId())
                .staterUser(instance.getStaterUser())
                .formAbstracts(instance.getFormAbstracts())
                .startTime(instance.getStartTime())
                .finishTime(instance.getFinishTime())
                .deployId(instance.getDeployId())
                .version(instance.getVersion())
                .formId(instance.getFormId())
                .superInstanceId(instance.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .build();
    }

    /**
     * 将抄送的流程转换为综合流程VO（带分组名称映射）
     */
    private CombinedProcessVo convertCcToCombined(ProcessInstanceVo instance, Map<Long, String> groupNameMap) {
        ProcessGroupInfo groupInfo = getProcessGroupInfo(instance.getProcessDefId());
        String groupName = groupInfo.getGroupId() != null ? groupNameMap.get(groupInfo.getGroupId()) : null;

        return CombinedProcessVo.builder()
                .processType(CombinedProcessVo.ProcessType.CC)
                .instanceId(instance.getInstanceId())
                .processDefId(instance.getProcessDefId())
                .processDefName(instance.getProcessDefName())
                .instanceName(instance.getInstanceName())
                .nodeId(instance.getNodeId())
                .taskName(instance.getTaskName())
                .status(instance.getStatus())
                .result(instance.getResult())
                .staterUserId(instance.getStaterUserId())
                .staterUser(instance.getStaterUser())
                .formAbstracts(instance.getFormAbstracts())
                .startTime(instance.getStartTime())
                .finishTime(instance.getFinishTime())
                .deployId(instance.getDeployId())
                .version(instance.getVersion())
                .formId(instance.getFormId())
                .superInstanceId(instance.getSuperInstanceId())
                .groupId(groupInfo.getGroupId())
                .groupName(groupName)
                .build();
    }

    /**
     * 获取抄送的流程实例信息
     *
     * @param instanceId 实例ID
     * @return 抄送我的流程
     */
    private List<ProcessProgressVo.ProgressNode> getCcTaskRecords(String instanceId) {
        Set<String> ccUsers = new HashSet<>();
        List<ProcessProgressVo.ProgressNode> ccList = ccTasksMapper.selectList(new LambdaQueryWrapper<WflowCcTasks>()
                .eq(WflowCcTasks::getInstanceId, instanceId)).stream().map(task -> {
            ccUsers.add(task.getUserId());
            return ProcessProgressVo.ProgressNode.builder()
                    .nodeId(task.getNodeId())
                    .nodeType(NodeTypeEnum.CC)
                    .isFuture(false)
                    .name(task.getNodeName())
                    .comment(Collections.emptyList())
                    .user(OrgUser.builder().id(task.getUserId()).build())
                    .startTime(task.getCreateTime())
                    .finishTime(task.getCreateTime())
                    .build();
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ccUsers)) {
            Map<String, OrgUser> userMap = userDeptOrLeaderService.getUserMapByIds(ccUsers);
            ccList.stream().peek(v -> v.setUser(userMap.get(v.getUser().getId()))).collect(Collectors.toList());
        }
        return ccList;
    }

    /**
     * 构造流程实例列表
     *
     * @param instanceNodeMap 流程实例ID与对应节点ID映射
     * @param Instances       流程实例
     * @return 流程实例列表
     */
    private List<ProcessInstanceVo> getInstances(Map<String, String> instanceNodeMap, List<HistoricProcessInstance> Instances) {
        Set<String> staterUsers = new HashSet<>();

        //获取表单摘要数据
        Map<String, List<FormAbstractsVo>> formAbstractsVoMap = businessDataService.getInstanceAbstractDatas(Instances.stream()
                .collect(Collectors.toMap(HistoricProcessInstance::getId, HistoricProcessInstance::getProcessDefinitionId)));
        //构造流程实例列表数据
        Map<String, ProcessInstanceVo> runInst = new HashMap<>(instanceNodeMap.size());
        List<ProcessInstanceVo> instanceVos = Instances.stream().map(ist -> {
            staterUsers.add(ist.getStartUserId());
            ProcessResultEnum processResult = ProcessResultEnum.resolveResult(ist.getEndActivityId());
            ProcessInstanceVo instanceVo = ProcessInstanceVo.builder()
                    .processDefId(ist.getProcessDefinitionId())
                    .instanceId(ist.getId())
                    .instanceName(ist.getName())
                    .superInstanceId(Optional.ofNullable(ist.getSuperProcessInstanceId()).orElse(ist.getId()))
                    .nodeId(instanceNodeMap.get(ist.getId()))
                    .formId(ist.getProcessDefinitionKey())
                    .staterUserId(ist.getStartUserId())
                    .startTime(ist.getStartTime())
                    .finishTime(ist.getEndTime())
                    .processDefName(ist.getProcessDefinitionName())
                    .status(processResult.getDesc())
                    .result(processResult)
                    .version(ist.getProcessDefinitionVersion())
                    .formAbstracts(formAbstractsVoMap.getOrDefault(ist.getId(), Collections.emptyList()))
                    .build();
            if (ProcessResultEnum.RUNNING.equals(instanceVo.getResult())) {
                //没有结束，还在走流程，获取任务
                runInst.put(ist.getId(), instanceVo);
            } else {
                instanceVo.setTaskName(instanceVo.getResult().getDesc());
            }
            return instanceVo;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(runInst.keySet())) {
            taskService.createTaskQuery().processInstanceIdIn(runInst.keySet()).active().list().stream()
                    .collect(Collectors.groupingBy(Task::getProcessInstanceId)).forEach((istId, tasks) -> {
                        Optional.ofNullable(runInst.get(istId)).ifPresent(ist -> {
                            ist.setNodeId(Optional.ofNullable(ist.getNodeId()).orElseGet(() -> {
                                if (CollectionUtil.isNotEmpty(tasks)) {
                                    return tasks.get(0).getTaskDefinitionKey();
                                }
                                return null;
                            }));
                            ist.setTaskName(StrUtil.join("、", tasks.stream().map(TaskInfo::getName).collect(Collectors.toSet())));
                        });
                    });
        }
        if (CollectionUtil.isNotEmpty(staterUsers)) {
            Map<String, OrgUser> userMap = userDeptOrLeaderService.getUserMapByIds(staterUsers);
            return instanceVos.stream().map(v -> {
                //发起人
                v.setStaterUser(userMap.get(v.getStaterUserId()));
                return v;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取流程处理操作权限
     *
     * @param currentNode 当前的节点
     * @return 操作权限设置
     */
    private OperationPerm getOperationPerm(ProcessNode<?> currentNode) {
        OperationPerm operationPerm = null;
        if (Objects.nonNull(currentNode)) {
            if (NodeTypeEnum.ROOT.equals(currentNode.getType())) {
                RootProps props = (RootProps) currentNode.getProps();
                operationPerm = props.getOperationPerm();
                if (Objects.isNull(operationPerm)) {
                    operationPerm = OperationPerm.builder()
                            .complete(new OperationPerm.Operations("提交", true))
                            .build();
                }else {
                    OperationPerm.Operations agree = operationPerm.getAgree();
                    if (Objects.nonNull(agree) && Objects.isNull(operationPerm.getComplete())){
                        agree.setAlisa("提交");
                        operationPerm.setComplete(agree);
                    }
                    operationPerm.setAgree(null);
                }
            } else if (NodeTypeEnum.APPROVAL.equals(currentNode.getType())) {
                ApprovalProps props = (ApprovalProps) currentNode.getProps();
                operationPerm = props.getOperationPerm();
                if (Objects.isNull(operationPerm)) {
                    operationPerm = OperationPerm.builder()
                            .agree(new OperationPerm.Operations("同意", true))
                            .refuse(new OperationPerm.Operations("拒绝", true))
                            .transfer(new OperationPerm.Operations("转交", true))
                            .afterAdd(new OperationPerm.Operations("加签", true))
                            .recall(new OperationPerm.Operations("退回", true))
                            .build();
                }
            } else if (NodeTypeEnum.TASK.equals(currentNode.getType())) {
                ApprovalProps props = (ApprovalProps) currentNode.getProps();
                operationPerm = props.getOperationPerm();
                if (Objects.isNull(operationPerm)) {
                    operationPerm = OperationPerm.builder()
                            .complete(new OperationPerm.Operations("提交", true))
                            .transfer(new OperationPerm.Operations("转办", true))
                            .afterAdd(new OperationPerm.Operations("加签", true))
                            .recall(new OperationPerm.Operations("退回", true))
                            .export(new OperationPerm.Operations("导出", true))
                            .build();
                }else {
                    OperationPerm.Operations agree = operationPerm.getAgree();
                    if (Objects.nonNull(agree) && Objects.isNull(operationPerm.getComplete())){
                        agree.setAlisa("提交");
                        operationPerm.setComplete(agree);
                    }
                    operationPerm.setAgree(null);
                }
            }
        }
        return operationPerm;
    }

    private ProcessProgressVo.InstanceExternSetting getExternSetting(String setting, String startUser) {
        JSONObject parsed = JSONObject.parseObject(setting);
        return ProcessProgressVo.InstanceExternSetting.builder()
                .enableSign(parsed.getBoolean("sign"))
                .enableCancel(Boolean.TRUE.equals(parsed.getBoolean("enableCancel"))
                        && startUser.equals(UserUtil.getLoginUserId()))
                .build();
    }

    private Map<String, Object> getNodeProps(Map<String, ProcessNode<?>> nodeMap){
        return nodeMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                v -> null == v.getValue().getProps() ? new HashMap<>() : v.getValue().getProps()));
    }
    private String getFrom (String defId){
        // 根据 formInfoIds 查询表单信息
        List<WflowFormInfo> formInfos = getFormInfoId(defId,null);
        return JsonStreamMerger.mergeWithStream(formInfos,WflowFormInfo::getFormItems);
    }
    private String getNodeFrom (String id,String nodeId){
        // 查询关联的 formInfoId
        List<WflowFormInfo> formInfos = getFormInfoId(id,nodeId);
        return JsonStreamMerger.mergeWithStream(formInfos,WflowFormInfo::getFormItems);
    }
    private  List<WflowFormInfo> getFormInfoId(String defId,String nodeId){
        List<WflowNodeFormInfo> wflowNodeFormInfoList = wflowNodeFormInfoMapper.selectList(
                Wrappers.<WflowNodeFormInfo>lambdaQuery()
                        .select(WflowNodeFormInfo::getFormInfoId)
                        .eq(StringUtils.isNotBlank(defId),WflowNodeFormInfo::getProcessDefId, defId)
//                        .eq(StringUtils.isNotBlank(nodeId),WflowNodeFormInfo::getNodeId, nodeId)
        );
        // 提取 formInfoId 的值集合
        List<String> formInfoIds = wflowNodeFormInfoList.stream()
                .map(WflowNodeFormInfo::getFormInfoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 根据 formInfoIds 查询表单信息
        List<WflowFormInfo> formInfos = new ArrayList<>();
        if (!formInfoIds.isEmpty()) {
            formInfos = wflowFormInfoMapper.selectList(
                    Wrappers.<WflowFormInfo>lambdaQuery()
                            .select(WflowFormInfo::getFormItems)
                            .in(WflowFormInfo::getId, formInfoIds)
            );
        } else {
            log.warn("未找到关联的表单ID，instanceId={}", defId);
        }
        return formInfos;
    }

    /**
     * 为任务记录补充用户部门信息
     *
     * @param taskRecords 任务记录列表
     */
    private void enrichTaskRecordsWithDeptInfo(List<ProcessProgressVo.ProgressNode> taskRecords) {
        if (CollectionUtil.isEmpty(taskRecords)) {
            return;
        }

        // 提取所有用户ID（包括user和owner）
        Set<String> userIds = new HashSet<>();
        for (ProcessProgressVo.ProgressNode record : taskRecords) {
            if (record.getUser() != null && StrUtil.isNotBlank(record.getUser().getId()) && record.getUser().getId().matches("\\d+")) {
                userIds.add(record.getUser().getId());
            }
            if (record.getOwner() != null && StrUtil.isNotBlank(record.getOwner().getId())) {
                userIds.add(record.getOwner().getId());
            }
        }

        if (userIds.isEmpty()) {
            return;
        }

        // 批量查询用户部门信息
        List<UserVo> userDeptInfos = orgRepositoryService.getBatchUserDetail(userIds);

        // 将用户详情转换为Map，便于快速查找
        Map<String, UserVo> userInfoMap = userDeptInfos.stream()
                .collect(Collectors.toMap(UserVo::getUserId, userVo -> userVo));

        // 为每个任务记录补充部门和机构信息
        for (ProcessProgressVo.ProgressNode record : taskRecords) {
            // 为user补充部门和机构信息
            if (record.getUser() != null && StrUtil.isNotBlank(record.getUser().getId())) {
                enrichUserWithDeptAndInstitutionInfo(record.getUser(), userInfoMap);
            }

            // 为owner补充部门和机构信息
            if (record.getOwner() != null && StrUtil.isNotBlank(record.getOwner().getId())) {
                enrichUserWithDeptAndInstitutionInfo(record.getOwner(), userInfoMap);
            }
        }
    }

    /**
     * 为单个用户补充部门和机构信息
     *
     * @param user        用户对象
     * @param userInfoMap 用户信息映射
     */
    private void enrichUserWithDeptAndInstitutionInfo(OrgUser user, Map<String, UserVo> userInfoMap) {
        UserVo userInfo = userInfoMap.get(user.getId());
        if (userInfo != null) {
            // 设置部门信息（取第一个部门作为主部门）
            if (CollectionUtil.isNotEmpty(userInfo.getDepts())) {
                String primaryDeptName = userInfo.getDepts().get(0);
                user.setDeptName(primaryDeptName);
            }

            // 设置机构信息（取第一个机构作为主机构）
            if (CollectionUtil.isNotEmpty(userInfo.getOrgs())) {
                String primaryOrgName = userInfo.getOrgs().get(0);
                user.setInstitutionName(primaryOrgName);
            }

        }
    }
    public boolean isAtInitiatorNode(String processInstanceId) {
        // 1. 获取流程实例的发起人
        String initiator = (String) runtimeService.getVariable(
                processInstanceId,
                "initiator"
        );

        if (initiator == null) {
            // 如果未设置 initiator 变量，尝试从历史记录获取
            HistoricProcessInstance instance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
            initiator = instance.getStartUserId();
        }

        // 2. 获取当前活动任务
        List<Task> activeTasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .list();

        // 3. 检查当前任务是否由发起人处理
        for (Task task : activeTasks) {
            if (initiator != null && initiator.equals(task.getAssignee())) {
                return true;
            }
        }

        return false;
    }

}
