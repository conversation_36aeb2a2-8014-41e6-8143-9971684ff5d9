package com.wflow.workflow.controller;

import com.wflow.bean.dto.TodoRequestDto;
import com.wflow.utils.R;
import com.wflow.utils.UserUtil;
import com.wflow.workflow.bean.vo.ProcessStartParamsVo;
import com.wflow.workflow.service.ProcessInstanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> willian fu
 * @date : 2022/8/24
 */
@RestController
@RequestMapping("wflow/process")
@Tag(name = "审批流程管理")
public class ProcessInstanceController {

    @Autowired
    private ProcessInstanceService processService;

    /**
     * 查询工作台上方 我发起的、带我处理、关于我的统计数量
     *
     * @return 统计数据
     */
    @GetMapping("instance/count")
    @Operation(summary ="查询工作台上方 我发起的、带我处理、关于我的统计数量")
    public Object getProcessInstanceCount() {
        return R.ok(processService.getProcessInstanceCount());
    }

    /**
     * 发起审批流程
     *
     * @param defId  流程定义ID
     * @param params 参数
     * @return 操作结果
     */
    @PostMapping("start/{defId}")
    @Operation(summary ="发起审批流程")
    public Object startTheProcess(@PathVariable String defId,
                                  @RequestBody ProcessStartParamsVo params) {
        String instanceId = processService.startProcess(defId, params);
        return R.ok("启动流程实例 " + instanceId + " 成功");
    }

    /**
     * 获取审批流程实例待处理的任务列表
     *
     * @param instanceId 流程实例ID
     * @return 列表数据
     */
    @GetMapping("{instanceId}/taskList")
    @Operation(summary ="获取审批流程实例待处理的任务列表")
    public Object getProcessInstanceTaskList(@PathVariable String instanceId) {
        return R.ok(processService.getProcessInstanceTaskList(instanceId));
    }

    /**
     * 获取系统内发起的流程
     *
     * @return 列表数据
     */
    @PostMapping("mySubmitted")
    @Operation(summary ="获取系统内发起的流程")
    public Object getUserSubmittedList(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        String[] startTimes = todoRequestDto.getStartTimes();
        String keyword = todoRequestDto.getKeyword();
        Boolean finished = todoRequestDto.getFinished();
        String fieldId = todoRequestDto.getFieldId();
        String fieldVal = todoRequestDto.getFieldVal();
        Map<String, Object> form = todoRequestDto.getForm();
        return R.ok(processService.getUserSubmittedList(pageSize, pageNo, UserUtil.getLoginUserId(), code,
                finished, startTimes, keyword, fieldId, fieldVal, form, null));
    }

    /**
     * 获取系统中所有已发起的流程实例
     *
     * @return 列表数据
     */
    @PostMapping("submittedList")
    @Operation(summary ="获取系统中所有已发起的流程实例")
    public Object getSubmittedList(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        String[] startTimes = todoRequestDto.getStartTimes();
        String keyword = todoRequestDto.getKeyword();
        Boolean finished = todoRequestDto.getFinished();
        String fieldId = todoRequestDto.getFieldId();
        String fieldVal = todoRequestDto.getFieldVal();
        return R.ok(processService.getUserSubmittedList(pageSize, pageNo, null, code,
                finished, startTimes, keyword, fieldId, fieldVal, null, null));
    }

    /**
     * 查询流程表单数据及审批的进度步骤
     *
     * @param instanceId 流程实例ID
     * @param nodeId     当前获取流程人员关联的流程节点ID
     * @return 流程进度及表单详情
     */
    @GetMapping("progress/{instanceId}/{nodeId}")
    @Operation(summary ="查询流程表单数据及审批的进度步骤")
    public Object getProcessFormAndInstanceProgress(@PathVariable String instanceId,
                                                    @PathVariable(required = false) String nodeId) {
        return R.ok(processService.getInstanceProgress(nodeId, instanceId));
    }

    /**
     * 获取抄送我的事项
     * @return 超送我的审批实例
     */
    @PostMapping("ccMe")
    @Operation(summary ="获取抄送我的事项")
    public Object getCcMeInstance(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        String[] startTimes = todoRequestDto.getStartTimes();
        Map<String, Object> form = todoRequestDto.getForm();
        return R.ok(processService.getCcMeInstance(pageSize, pageNo, code, startTimes,form));
    }

    /**
     * 删除流程实例
     *
     * @param instanceId 实例ID
     * @return 操作结果
     */
    @DeleteMapping("instance/{instanceId}")
    @Operation(summary ="删除流程实例")
    public Object delProcessInstance(@PathVariable String instanceId) {
        processService.delProcessInstance(instanceId);
        return R.ok("删除流程实例成功");
    }

    /**
     * 结束流程
     *
     * @param instanceId 实例ID
     * @return 操作结果
     */
    @DeleteMapping("deleteReason/{instanceId}")
    @Operation(summary ="手动结束流程")
    public Object deleteReason(@PathVariable String instanceId) {
        return processService.deleteReason(instanceId);
    }

    /**
     * 获取系统中所有已发起的流程实例
     *
     * @return 列表数据
     */
    @PostMapping("historyList")
    @Operation(summary = "获取系统中历史的流程实例")
    public Object getHistoryList(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        String[] startTimes = todoRequestDto.getStartTimes();
        String keyword = todoRequestDto.getKeyword();
        Boolean finished = todoRequestDto.getFinished();
        String fieldId = todoRequestDto.getFieldId();
        String fieldVal = todoRequestDto.getFieldVal();
        String type = "history";
        return R.ok(processService.getHistoryList(pageSize, pageNo, null, code,
                finished, startTimes, keyword, fieldId, fieldVal, null, type));
    }

    /**
     * 获取用户相关的所有流程实例综合列表
     * 包括：待办任务、已办任务、发起的流程、抄送的流程
     *
     * @param todoRequestDto 查询请求参数
     * @return 综合流程列表数据
     */
    @PostMapping("combinedList")
    @Operation(summary = "获取用户相关的所有流程实例综合列表")
    public Object getUserCombinedProcessList(@RequestBody TodoRequestDto todoRequestDto) {
        Integer pageSize = todoRequestDto.getPageSize();
        Integer pageNo = todoRequestDto.getPageNo();
        String code = todoRequestDto.getCode();
        String[] startTimes = todoRequestDto.getStartTimes();
        String keyword = todoRequestDto.getKeyword();
        Map<String, Object> form = todoRequestDto.getForm();
        Long groupId = todoRequestDto.getGroupId();

        return R.ok(processService.getUserCombinedProcessList(pageSize, pageNo, code,
                startTimes, keyword, form, groupId));
    }

}
