package com.wflow.workflow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.workflow.bean.vo.*;
import org.flowable.task.api.Task;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> willian fu
 * @date : 2022/8/22
 */
public interface ProcessInstanceService {

    String startProcess(String defId, ProcessStartParamsVo params);

    String getBeforeTask(String instanceId, String task);

    void delProcessInstance(String instanceId);

    ProcessProgressVo getInstanceProgress(String nodeId, String instanceId);

    List<Task> getProcessInstanceTaskList(String instanceId);

    /**
     * 获取用户发起的流程实例
     * @param pageSize 每页数量
     * @param pageNo 页码
     * @param startUser  发起人
     * @param code 表单流程ID（流程定义KEY）
     * @param finished 流程是否已经结束
     * @param startTimes 发起流程的时间范围
     * @param keyword 关键字（流程实例名、发起人名）
     * @param fieldId 字段ID
     * @param fieldVal 字段值
     * @return 列表数据
     */
    Page<ProcessInstanceVo> getUserSubmittedList(Integer pageSize, Integer pageNo, String startUser, String code,
                                                 Boolean finished, String[] startTimes, String keyword,
            String fieldId, String fieldVal, Map<String, Object> form, String type);

    /**
     * 获取系统抄送我的流程
     *
     * @param pageSize  每页数量
     * @param pageNo    页码
     * @param code      表单流程ID（流程定义KEY）
     * @param startTimes 抄送开始时间范围
     * @return 列表数据
     */
    Page<ProcessInstanceVo> getCcMeInstance(Integer pageSize, Integer pageNo, String code, String[] startTimes,Map<String, Object> form);

    InstanceCountVo getProcessInstanceCount();

    ResponseEntity<Object> deleteReason(String instanceId);

    Object getHistoryList(Integer pageSize, Integer pageNo, Object o, String code, Boolean finished,
            String[] startTimes, String keyword, String fieldId, String fieldVal, Object o1, String type);

    /**
     * 获取用户相关的所有流程实例综合列表
     * 
     * @param pageSize   每页数量
     * @param pageNo     页码
     * @param code       表单流程ID（流程定义KEY）
     * @param startTimes 时间范围
     * @param keyword    关键字
     * @param form       表单查询条件
     * @param groupId    流程分组ID
     * @return 综合流程列表数据
     */
    Page<CombinedProcessVo> getUserCombinedProcessList(Integer pageSize, Integer pageNo, String code,
            String[] startTimes, String keyword,
            Map<String, Object> form, Long groupId);
}
