package com.wflow.bean.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "流程各个状态查询请求")
public class TodoRequestDto {
    private Integer pageSize = 20;
    private Integer pageNo = 1;
    @Schema(description = "流程定义key")
    private String code;
    @Schema(description = "开始时间")
    private String[] startTimes;
    @Schema(description = " 关键字(任务节点名称、流程名称)")
    private String keyword;
    @Schema(description = "查询项")
    private Map<String, Object> form;
    @Schema(description = "查询类型,查询已结束还是未结束")
    private String type;
    @Schema(description = "流程是否结束")
    private Boolean finished;
    @Schema(description = "字段ID")
    private String fieldId;
    @Schema(description = "字段值")
    private String fieldVal;
    @Schema(description = "项目id")
    private String projectId;
}
